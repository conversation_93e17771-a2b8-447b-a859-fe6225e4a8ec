import { useGetUnreadCountQuery } from '@/store/api/chatApiSlice';

export const useUnreadMessages = () => {
  const { data, isLoading, error, refetch } = useGetUnreadCountQuery(
    undefined,
    {
      pollingInterval: 30000, // Poll every 30 seconds
      refetchOnFocus: true,
      refetchOnReconnect: true,
      skip: !localStorage.getItem('userId'),
    }
  );

  return {
    unreadCount: data?.unreadCount || 0,
    isLoading,
    error,
    refetch,
  };
};
