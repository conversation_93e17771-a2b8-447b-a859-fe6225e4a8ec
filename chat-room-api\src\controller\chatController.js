const { Conversation, Message, AuditLog } = require('../models');
const { body, validationResult } = require('express-validator');

// Validation rules
const messageValidation = [
  // Support both legacy 'message' and new 'content' field names
  body('content')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Message content cannot be empty')
    .isLength({ max: 1000 })
    .withMessage('Message content too long (max 1000 characters)'),
  body('message')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Message content cannot be empty')
    .isLength({ max: 1000 })
    .withMessage('Message content too long (max 1000 characters)'),
  // Support both legacy 'messageType' and new 'type' field names
  body('type')
    .optional()
    .isIn(['text', 'image', 'file', 'audio', 'video'])
    .withMessage('Invalid message type'),
  body('messageType')
    .optional()
    .isIn(['text', 'image', 'file', 'audio', 'video'])
    .withMessage('Invalid message type'),
  // Custom validation to ensure at least one content field is provided
  body()
    .custom((value, { req }) => {
      const { content, message } = value;
      if (!content && !message) {
        throw new Error('Either content or message field is required');
      }
      return true;
    })
];

const conversationValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Title too long (max 100 characters)'),
  body('nurseId')
    .optional()
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Nurse ID must be a non-empty string'),
  body('customerId')
    .optional()
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Patient ID must be a non-empty string'),
  // Custom validation to ensure at least one participant ID is provided
  body()
    .custom((value, { req }) => {
      const { nurseId, customerId } = value;
      const { type: userType } = req.user;
      
      if (userType === 'patient' && !nurseId) {
        throw new Error('Nurse ID is required when creating conversation as a patient');
      }
      if (userType === 'nurse' && !customerId) {
        throw new Error('Patient ID is required when creating conversation as a nurse');
      }
      
      return true;
    })
];

// Helper function to get conversation with messages
const getConversationWithMessages = async (conversationId, page = 1, limit = 50) => {
  const conversation = await Conversation.getConversation(conversationId);
  if (!conversation) {
    return null;
  }
  
  const messages = await Message.getMessages(conversationId, page, limit);
  
  return {
    conversation,
    messages,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: messages.length
    }
  };
};

// Create a new conversation
const createConversation = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errors.array()
      });
    }

    const { title, nurseId, customerId } = req.body;
    const { id: userId, type: userType } = req.user;

    console.log(`Creating conversation - User: ${userId} (${userType}), Title: ${title}`);

    // Enhanced validation based on user type
    if (userType === 'patient') {
      if (!nurseId) {
        return res.status(400).json({
          success: false,
          message: 'Nurse ID is required for patient to create conversation',
          error: 'MISSING_NURSE_ID'
        });
      }
      // Validate that patient is not trying to create conversation with themselves
      if (nurseId === userId) {
        return res.status(400).json({
          success: false,
          message: 'Cannot create conversation with yourself',
          error: 'INVALID_PARTICIPANTS'
        });
      }
    } else if (userType === 'nurse') {
      if (!customerId) {
        return res.status(400).json({
          success: false,
          message: 'Patient ID is required for nurse to create conversation',
          error: 'MISSING_PATIENT_ID'
        });
      }
      // Validate that nurse is not trying to create conversation with themselves
      if (customerId === userId) {
        return res.status(400).json({
          success: false,
          message: 'Cannot create conversation with yourself',
          error: 'INVALID_PARTICIPANTS'
        });
      }
    } else {
      return res.status(403).json({
        success: false,
        message: 'Only patients and nurses can create conversations',
        error: 'INVALID_USER_TYPE'
      });
    }

    // Determine the participants based on user type
    let finalcustomerId, finalNurseId;
    if (userType === 'patient') {
      finalcustomerId = userId;
      finalNurseId = nurseId;
    } else if (userType === 'nurse') {
      finalNurseId = userId;
      finalcustomerId = customerId;
    }

    // Optional: Check for existing conversations (configurable via environment variable)
    const preventDuplicates = process.env.PREVENT_DUPLICATE_CONVERSATIONS !== 'false';
    
    if (preventDuplicates) {
      const existingConversation = await Conversation.findOne({
        $or: [
          { customerId: finalcustomerId, nurseId: finalNurseId, status: 'active' },
          { customerId: finalNurseId, nurseId: finalcustomerId, status: 'active' }
        ]
      });

      if (existingConversation) {
        console.log(`Existing conversation found - ID: ${existingConversation._id}`);
        
        // Get conversation with messages using helper function
        const conversationWithMessages = await getConversationWithMessages(existingConversation._id.toString(), 1, 50);
        
        if (!conversationWithMessages) {
          return res.status(404).json({
            success: false,
            message: 'Conversation not found'
          });
        }
        
        // Return the existing conversation with messages
        return res.status(200).json({
          success: true,
          message: 'Existing conversation retrieved successfully',
          data: conversationWithMessages
        });
      }
    }

    // Create the conversation
    const conversation = await Conversation.createConversation(
      finalcustomerId,
      finalNurseId,
      title
    );

    console.log(`Conversation created successfully - ID: ${conversation._id}`);

    // Log the conversation creation for audit purposes
    try {
      await AuditLog.create({
        action: 'conversation_created',
        userId: userId,
        userType: userType,
        resource: 'conversation',
        resourceId: conversation._id.toString(),
        details: {
          customerId: finalcustomerId,
          nurseId: finalNurseId,
          title: title,
          createdBy: userType
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
    } catch (auditError) {
      console.warn('Failed to create audit log for conversation creation:', auditError);
    }

    // Broadcast to WebSocket if available
    if (global.wsManager) {
      const wsMessage = {
        type: 'CONVERSATION_CREATED',
        conversationId: conversation._id.toString(),
        customerId: finalcustomerId,
        nurseId: finalNurseId,
        title: title,
        createdBy: userType,
        timestamp: new Date().toISOString()
      };
      
      // Notify both participants
      global.wsManager.sendToUser(finalcustomerId, wsMessage);
      global.wsManager.sendToUser(finalNurseId, wsMessage);
    }

    res.status(201).json({
      success: true,
      message: 'Conversation created successfully',
      data: {
        conversation: {
          id: conversation._id,
          customerId: finalcustomerId,
          nurseId: finalNurseId,
          title: title,
          status: conversation.status,
          createdAt: conversation.createdAt,
          createdBy: userType
        }
      }
    });
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create conversation',
      error: error.message
    });
  }
};

// Send a message
const sendMessage = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errors.array()
      });
    }

    const { conversationId } = req.params;
    // Support both legacy and new field names
    const content = req.body.content || req.body.message;
    const messageType = req.body.type || req.body.messageType || 'text';
    const { id: senderId, type: senderType } = req.user;

    const newMessage = await Message.sendMessage(
      conversationId,
      senderId,
      senderType,
      content,
      messageType
    );
    const messageId = newMessage._id;

    // Broadcast message to WebSocket clients
    if (global.wsManager) {
      const wsMessage = {
        type: 'TEXT_MESSAGE',
        conversationId: conversationId,
        senderId: senderId,
        senderType: senderType,
        senderName: senderType === 'nurse' ? 'Nurse' : 'Patient', // TODO: Get actual names
        content,
        messageType,
        messageId: messageId.toString(),
        timestamp: newMessage.createdAt ? newMessage.createdAt.toISOString() : new Date().toISOString()
      };

      global.wsManager.broadcastToRoom(conversationId, wsMessage);
    }

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        messageId,
        conversationId,
        content,
        messageType,
        senderId,
        senderType,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message',
      error: error.message
    });
  }
};

// Get conversation messages
const getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    const messages = await Message.getMessages(
      conversationId,
      parseInt(page),
      parseInt(limit)
    );

    res.json({
      success: true,
      message: 'Messages retrieved successfully',
      data: {
        messages,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: messages.length
        }
      }
    });
  } catch (error) {
    console.error('Error getting messages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve messages',
      error: error.message
    });
  }
};

// Get user conversations
const getUserConversations = async (req, res) => {
  try {
    const { id: userId, type: userType } = req.user;
    const { page = 1, limit = 20 } = req.query;

    const conversations = await Conversation.getUserConversations(
      userId,
      userType,
      parseInt(page),
      parseInt(limit)
    );

    // Get unread counts for each conversation
    const conversationsWithUnreadCounts = await Promise.all(
      conversations.map(async (conversation) => {
        const unreadCount = await Message.getUnreadCountForUser(conversation._id.toString(), userId);
        return {
          ...conversation,
          unreadCount
        };
      })
    );

    res.json({
      success: true,
      message: 'Conversations retrieved successfully',
      data: {
        conversations: conversationsWithUnreadCounts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: conversationsWithUnreadCounts.length
        }
      }
    });
  } catch (error) {
    console.error('Error getting user conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve conversations',
      error: error.message
    });
  }
};

// Get conversation details
const getConversation = async (req, res) => {
  try {
    const { conversationId } = req.params;

    const conversation = await Conversation.getConversation(conversationId);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    res.json({
      success: true,
      message: 'Conversation retrieved successfully',
      data: conversation
    });
  } catch (error) {
    console.error('Error getting conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve conversation',
      error: error.message
    });
  }
};

// Mark conversation as inactive
const markConversationInactive = async (req, res) => {
  try {
    const { conversationId } = req.params;

    const conversation = await Conversation.findById(conversationId);
    if (conversation) {
      await conversation.markInactive();
    }

    res.json({
      success: true,
      message: 'Conversation marked as inactive successfully'
    });
  } catch (error) {
    console.error('Error marking conversation inactive:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark conversation inactive',
      error: error.message
    });
  }
};

// Get active conversations count
const getActiveConversationsCount = async (req, res) => {
  try {
    const { id: userId, type: userType } = req.user;

    const count = await Conversation.getActiveConversationsCount(userId, userType);

    res.json({
      success: true,
      message: 'Active conversations count retrieved successfully',
      data: {
        count,
        userType
      }
    });
  } catch (error) {
    console.error('Error getting active conversations count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve active conversations count',
      error: error.message
    });
  }
};

// Get total unread count for user
const getUnreadCount = async (req, res) => {
  try {
    const { id: userId, type: userType } = req.user;

    const conversations = await Conversation.getUserConversations(userId, userType, 1, 1000);

    let totalUnreadCount = 0;
    for (const conversation of conversations) {
      const unreadCount = await Message.getUnreadCountForUser(conversation._id.toString(), userId);
      totalUnreadCount += unreadCount;
    }

    res.json({
      success: true,
      message: 'Unread count retrieved successfully',
      data: {
        unreadCount: totalUnreadCount
      }
    });
  } catch (error) {
    console.error('Error getting unread count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve unread count',
      error: error.message
    });
  }
};

// Search messages in conversation
const searchMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { q: searchTerm } = req.query;

    if (!searchTerm || searchTerm.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search term must be at least 2 characters long'
      });
    }

    const messages = await Message.searchMessages(conversationId, searchTerm.trim());

    res.json({
      success: true,
      message: 'Search completed successfully',
      data: {
        messages,
        searchTerm: searchTerm.trim(),
        total: messages.length
      }
    });
  } catch (error) {
    console.error('Error searching messages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search messages',
      error: error.message
    });
  }
};

// Get conversation statistics for scrutiny
const getConversationStats = async (req, res) => {
  try {
    const { conversationId } = req.params;

    // Get conversation details
    const conversation = await Conversation.getConversation(conversationId);
    
    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    // Get message statistics
    const statistics = await Chat.getConversationStats(conversationId);

    res.json({
      success: true,
      message: 'Conversation statistics retrieved successfully',
      data: {
        conversation,
        statistics
      }
    });
  } catch (error) {
    console.error('Error getting conversation stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve conversation statistics',
      error: error.message
    });
  }
};

// Mark messages as read
const markMessagesAsRead = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { id: userId } = req.user;

    // Mark all unread messages in the conversation as read
    const result = await Message.markAsRead(conversationId, userId);

    // Create audit log for the read action
    const auditLog = new AuditLog({
      action: 'message_read',
      resource: 'message',
      resourceId: conversationId,
      userId: userId,
      userType: req.user.type,
      details: {
        conversationId: conversationId,
        messagesMarked: result.modifiedCount
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });
    await auditLog.save();

    // Broadcast read receipt to WebSocket clients
    if (global.wsManager) {
      const wsMessage = {
        type: 'READ_RECEIPT',
        conversationId: conversationId,
        senderId: userId,
        senderType: req.user.type,
        timestamp: new Date().toISOString()
      };

      global.wsManager.broadcastToRoom(conversationId, wsMessage, userId);

      // Also broadcast CONVERSATION_READ for consistency
      const conversationReadMessage = {
        type: 'CONVERSATION_READ',
        conversationId: conversationId,
        readBy: userId,
        readByType: req.user.type,
        messagesMarked: result.modifiedCount,
        timestamp: new Date().toISOString()
      };

      global.wsManager.broadcastToRoom(conversationId, conversationReadMessage, userId);
    }

    res.json({
      success: true,
      message: 'Messages marked as read successfully',
      data: {
        conversationId,
        messagesMarked: result.modifiedCount,
        readBy: userId,
        readAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark messages as read',
      error: error.message
    });
  }
};

// Get conversation with messages
const getConversationWithMessagesEndpoint = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const { id: userId } = req.user;

    const conversationWithMessages = await getConversationWithMessages(
      conversationId, 
      parseInt(page), 
      parseInt(limit)
    );

    if (!conversationWithMessages) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    // Get unread count for this conversation
    const unreadCount = await Message.getUnreadCountForUser(conversationId, userId);

    // Add unread count to the response
    const responseData = {
      ...conversationWithMessages,
      unreadCount
    };

    res.json({
      success: true,
      message: 'Conversation with messages retrieved successfully',
      data: responseData
    });
  } catch (error) {
    console.error('Error getting conversation with messages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve conversation with messages',
      error: error.message
    });
  }
};

module.exports = {
  createConversation,
  sendMessage,
  getMessages,
  getUserConversations,
  getConversation,
  markConversationInactive,
  getActiveConversationsCount,
  getUnreadCount,
  searchMessages,
  getConversationStats,
  markMessagesAsRead,
  messageValidation,
  conversationValidation,
  getConversationWithMessagesEndpoint
};